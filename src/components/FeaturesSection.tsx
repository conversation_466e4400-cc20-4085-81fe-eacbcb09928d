import React from 'react';
import WhatsAppFeature from './features/WhatsAppFeature';
import EmailScannerFeature from './features/EmailScannerFeature';
import DataAccessibilityFeature from './features/DataAccessibilityFeature';
import AIReportingFeature from './features/AIReportingFeature';

const FeaturesSection: React.FC = () => {
  return (
    <section className="py-24 bg-background relative overflow-hidden" dir="rtl">
      {/* Background Elements */}
      <div className="absolute inset-0 cosmic-grid opacity-30"></div>
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-muted/20 text-muted-foreground px-4 py-2 rounded-full text-sm font-medium mb-6">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            יכולות מתקדמות
          </div>
          
          <h2 className="text-4xl lg:text-6xl font-bold text-foreground mb-6">
            טכנולוגיה שמשנה את המשחק
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            פתרונות AI מתקדמים שהופכים את ניהול הכספים לפשוט, חכם ויעיל יותר מאי פעם
          </p>
        </div>

        {/* Features */}
        <div className="space-y-32">
          {/* Feature 1: WhatsApp AI Assistant */}
          <div className="cosmic-glass rounded-3xl p-8 lg:p-12">
            <WhatsAppFeature />
          </div>

          {/* Feature 2: AI Email Scanner */}
          <div className="cosmic-glass rounded-3xl p-8 lg:p-12">
            <EmailScannerFeature />
          </div>

          {/* Feature 3: Easy Data Accessibility */}
          <div className="cosmic-glass rounded-3xl p-8 lg:p-12">
            <DataAccessibilityFeature />
          </div>

          {/* Feature 4: AI Reporting */}
          <div className="cosmic-glass rounded-3xl p-8 lg:p-12">
            <AIReportingFeature />
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-20">
          <div className="cosmic-glass rounded-2xl p-8 lg:p-12 max-w-4xl mx-auto">
            <h3 className="text-3xl lg:text-4xl font-bold text-foreground mb-4">
              מוכנים לחוות את העתיד?
            </h3>
            <p className="text-lg text-muted-foreground mb-8">
              הצטרפו לאלפי עסקים שכבר משתמשים בפתרונות AI שלנו לניהול פיננסי חכם
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-foreground text-background rounded-xl font-medium hover:shadow-lg transition-all duration-300 hover:scale-105">
                התחילו עכשיו בחינם
              </button>
              <button className="px-8 py-4 border border-border text-foreground rounded-xl font-medium hover:bg-muted/50 transition-all duration-300">
                צפו בהדגמה
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
