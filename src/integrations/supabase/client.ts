// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://swjkxvmnltxproyeicti.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3amt4dm1ubHR4cHJveWVpY3RpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYxMDc1NDksImV4cCI6MjA3MTY4MzU0OX0.BHBL0rC--5u8pb7is23q24Pvg4vRmVFDl2i33GrtObk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});