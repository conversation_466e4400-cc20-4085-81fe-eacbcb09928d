export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      call_logs: {
        Row: {
          abbreviation: string | null
          address: string | null
          agent_name: string
          alt_phone: string | null
          alt_phone_area_code: string | null
          alt_phone_without_area_code: string | null
          call_log_id: string | null
          call_notes: string | null
          call_source: string | null
          call_type: string | null
          city: string | null
          company_name: string | null
          consent_date: string | null
          country: string | null
          county: string | null
          created_at: string
          current_campaign: string | null
          education: string | null
          email: string | null
          est_value: string | null
          estimated_value: string | null
          first_name: string | null
          full_address: string | null
          house_sqft: string | null
          id: string
          last_name: string | null
          log_time: string
          log_time_date: string | null
          log_time_time: string | null
          log_type: string
          marital_status: string | null
          original_campaign: string | null
          original_lead_file: string | null
          original_lead_id: string | null
          owner_1: string | null
          owner_2: string | null
          phone: string | null
          phone_area_code: string | null
          phone_without_area_code: string | null
          profile_id: string | null
          recording_end: string | null
          recording_id: string | null
          recording_length: string | null
          recording_length_seconds: number | null
          recording_local_phone: string | null
          recording_remote_phone: string | null
          recording_start: string | null
          roof_cover: string | null
          scheduled_time: string | null
          sent_to: string | null
          sqft: string | null
          state: string | null
          updated_at: string
          user_login_id: string | null
          zip_code: string | null
        }
        Insert: {
          abbreviation?: string | null
          address?: string | null
          agent_name: string
          alt_phone?: string | null
          alt_phone_area_code?: string | null
          alt_phone_without_area_code?: string | null
          call_log_id?: string | null
          call_notes?: string | null
          call_source?: string | null
          call_type?: string | null
          city?: string | null
          company_name?: string | null
          consent_date?: string | null
          country?: string | null
          county?: string | null
          created_at?: string
          current_campaign?: string | null
          education?: string | null
          email?: string | null
          est_value?: string | null
          estimated_value?: string | null
          first_name?: string | null
          full_address?: string | null
          house_sqft?: string | null
          id?: string
          last_name?: string | null
          log_time: string
          log_time_date?: string | null
          log_time_time?: string | null
          log_type: string
          marital_status?: string | null
          original_campaign?: string | null
          original_lead_file?: string | null
          original_lead_id?: string | null
          owner_1?: string | null
          owner_2?: string | null
          phone?: string | null
          phone_area_code?: string | null
          phone_without_area_code?: string | null
          profile_id?: string | null
          recording_end?: string | null
          recording_id?: string | null
          recording_length?: string | null
          recording_length_seconds?: number | null
          recording_local_phone?: string | null
          recording_remote_phone?: string | null
          recording_start?: string | null
          roof_cover?: string | null
          scheduled_time?: string | null
          sent_to?: string | null
          sqft?: string | null
          state?: string | null
          updated_at?: string
          user_login_id?: string | null
          zip_code?: string | null
        }
        Update: {
          abbreviation?: string | null
          address?: string | null
          agent_name?: string
          alt_phone?: string | null
          alt_phone_area_code?: string | null
          alt_phone_without_area_code?: string | null
          call_log_id?: string | null
          call_notes?: string | null
          call_source?: string | null
          call_type?: string | null
          city?: string | null
          company_name?: string | null
          consent_date?: string | null
          country?: string | null
          county?: string | null
          created_at?: string
          current_campaign?: string | null
          education?: string | null
          email?: string | null
          est_value?: string | null
          estimated_value?: string | null
          first_name?: string | null
          full_address?: string | null
          house_sqft?: string | null
          id?: string
          last_name?: string | null
          log_time?: string
          log_time_date?: string | null
          log_time_time?: string | null
          log_type?: string
          marital_status?: string | null
          original_campaign?: string | null
          original_lead_file?: string | null
          original_lead_id?: string | null
          owner_1?: string | null
          owner_2?: string | null
          phone?: string | null
          phone_area_code?: string | null
          phone_without_area_code?: string | null
          profile_id?: string | null
          recording_end?: string | null
          recording_id?: string | null
          recording_length?: string | null
          recording_length_seconds?: number | null
          recording_local_phone?: string | null
          recording_remote_phone?: string | null
          recording_start?: string | null
          roof_cover?: string | null
          scheduled_time?: string | null
          sent_to?: string | null
          sqft?: string | null
          state?: string | null
          updated_at?: string
          user_login_id?: string | null
          zip_code?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
